@tailwind base;
@tailwind components;
@tailwind utilities;

/* <PERSON><PERSON> */
.neon-blue-purple {
  border-image: linear-gradient(45deg, #3b82f6, #8b5cf6) 1;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3),
    inset 0 0 10px rgba(59, 130, 246, 0.1);
}

.neon-blue-purple:hover {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.7), 0 0 30px rgba(139, 92, 246, 0.5),
    0 0 45px rgba(59, 130, 246, 0.3), inset 0 0 15px rgba(139, 92, 246, 0.2);
}

.neon-red-orange {
  border-image: linear-gradient(45deg, #ef4444, #f97316) 1;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(249, 115, 22, 0.3),
    inset 0 0 10px rgba(239, 68, 68, 0.1);
}

.neon-red-orange:hover {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.7), 0 0 30px rgba(249, 115, 22, 0.5),
    0 0 45px rgba(239, 68, 68, 0.3), inset 0 0 15px rgba(249, 115, 22, 0.2);
}

.neon-purple-magenta {
  border-image: linear-gradient(45deg, #8b5cf6, #ec4899) 1;
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5), 0 0 20px rgba(236, 72, 153, 0.3),
    inset 0 0 10px rgba(139, 92, 246, 0.1);
}

.neon-purple-magenta:hover {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.7), 0 0 30px rgba(236, 72, 153, 0.5),
    0 0 45px rgba(139, 92, 246, 0.3), inset 0 0 15px rgba(236, 72, 153, 0.2);
}

.neon-orange-yellow {
  border-image: linear-gradient(45deg, #f97316, #eab308) 1;
  box-shadow: 0 0 10px rgba(249, 115, 22, 0.5), 0 0 20px rgba(234, 179, 8, 0.3),
    inset 0 0 10px rgba(249, 115, 22, 0.1);
}

.neon-orange-yellow:hover {
  box-shadow: 0 0 15px rgba(249, 115, 22, 0.7), 0 0 30px rgba(234, 179, 8, 0.5),
    0 0 45px rgba(249, 115, 22, 0.3), inset 0 0 15px rgba(234, 179, 8, 0.2);
}

@layer base {
  :root {
    --background: 210 40% 98%; /* Slightly off-white, very light grey-blue */
    --foreground: 222.2 47.4% 11.2%; /* Dark blue-grey */

    --card: 210 40% 98%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 210 40% 98%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --primary: 220.9 39.3% 39.3%; /* A professional, muted blue */
    --primary-foreground: 210 40% 98%; /* Light background for text on primary */
    --primary-hover: 220.9 39.3% 30%; /* Darker shade of primary for hover */

    --secondary: 210 40% 96.1%; /* Light grey */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 40%; /* Darker grey for better contrast in light mode */

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 214.3 31.8% 91.4%; /* Light grey-blue border */
    --input: 214.3 31.8% 91.4%;
    --ring: 220.9 39.3% 39.3%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --radius: 0.75rem; /* Slightly larger radius for softer corners */

    --sidebar-background: 210 40% 96%; /* Slightly darker than main background, subtle distinction */
    --sidebar-foreground: 222.2 47.4% 11.2%;
    --sidebar-primary: 220.9 39.3% 39.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 94%; /* Slightly darker hover state */
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 88%; /* Slightly darker border */
    --sidebar-ring: 220.9 39.3% 39.3%;

    --jimeka-blue: 210 100% 66%; /* HSL for #52a9ff */
  }

  .dark {
    --background: 222.2 47.4% 11.2%; /* Dark blue-grey */
    --foreground: 210 40% 98%; /* Light background */

    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 65% 45%; /* Reduced saturation and lightness for softer blue */
    --primary-foreground: 0 0% 98%; /* Corrected: Light foreground for text on primary in dark mode */
    --primary-hover: 217.2 70% 35%; /* Darker shade of primary for hover in dark mode */

    --secondary: 217.2 32.6% 17.5%; /* Darker grey-blue */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 75%; /* Improved contrast - lighter grey for better readability */

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 217.2 32.6% 17.5%; /* Darker grey-blue border */
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 65% 45%; /* Match with primary for consistency */

    --chart-1: 220 60% 40%; /* Reduced brightness for charts */
    --chart-2: 160 50% 35%;
    --chart-3: 30 70% 45%;
    --chart-4: 280 55% 50%;
    --chart-5: 340 65% 45%;

    --radius: 0.75rem;

    --sidebar-background: 222.2 47.4% 10%; /* Even darker, almost black-blue */
    --sidebar-foreground: 210 40% 98%; /* High contrast white text */
    --sidebar-primary: 217.2 75% 70%; /* Even brighter for better visibility and contrast */
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 18%; /* Improved contrast for hover states */
    --sidebar-accent-foreground: 210 40% 98%; /* High contrast white text */
    --sidebar-border: 217.2 32.6% 15%;
    --sidebar-ring: 217.2 75% 65%; /* Match with sidebar primary for consistency */

    --jimeka-blue: 210 75% 50%; /* Reduced saturation and lightness for softer appearance */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Accessibility improvements for better contrast */
@layer utilities {
  /* High contrast text for better readability */
  .text-high-contrast {
    color: hsl(210 40% 85%) !important;
  }

  .dark .text-high-contrast {
    color: hsl(210 40% 90%) !important;
  }

  /* Improve contrast for sidebar elements */
  .sidebar .text-muted-foreground {
    color: hsl(215 20.2% 75%) !important;
  }

  .dark .sidebar .text-muted-foreground {
    color: hsl(215 20.2% 80%) !important;
  }

  /* Better contrast for analytics components */
  .analytics-card .text-muted-foreground {
    color: hsl(215.4 16.3% 35%) !important;
  }

  .dark .analytics-card .text-muted-foreground {
    color: hsl(215 20.2% 78%) !important;
  }

  /* Screen reader only text */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}
